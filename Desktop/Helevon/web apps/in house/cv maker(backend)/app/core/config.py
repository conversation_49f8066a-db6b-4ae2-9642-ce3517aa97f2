"""
Configuration settings for the CV Maker API.

This module contains all configuration settings using Pydantic Settings
for environment variable management and validation.
"""

import os
from typing import List, Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings with environment variable support.
    
    All settings can be overridden via environment variables.
    """
    
    # Application Settings
    DEBUG: bool = Field(default=True, description="Enable debug mode")
    SECRET_KEY: str = Field(default="your-secret-key-change-in-production", description="Secret key for JWT")
    ALGORITHM: str = Field(default="HS256", description="JWT algorithm")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="Access token expiration in minutes")
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, description="Refresh token expiration in days")
    
    # Database Settings
    DATABASE_URL: Optional[str] = Field(default=None, description="Database URL for development")
    DATABASE_URL_PROD: Optional[str] = Field(default=None, description="Database URL for production")
    
    # CORS Settings
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8000", "http://127.0.0.1:3000"],
        description="Allowed CORS origins"
    )
    
    # File Upload Settings
    MAX_UPLOAD_SIZE_MB: int = Field(default=5, description="Maximum file upload size in MB")
    ALLOWED_FILE_TYPES: List[str] = Field(
        default=["image/jpeg", "image/png", "image/jpg", "application/pdf"],
        description="Allowed file MIME types"
    )
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = Field(default=100, description="Rate limit requests per window")
    RATE_LIMIT_WINDOW_MS: int = Field(default=900000, description="Rate limit window in milliseconds")
    
    # PDF Export
    CAN_EXPORT: bool = Field(default=True, description="Enable PDF export functionality")
    
    # Server Settings
    HOST: str = Field(default="0.0.0.0", description="Server host")
    PORT: int = Field(default=8000, description="Server port")
    WORKERS: int = Field(default=1, description="Number of worker processes for production")
    
    @validator("ALLOWED_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("ALLOWED_FILE_TYPES", pre=True)
    def parse_file_types(cls, v):
        """Parse allowed file types from string or list."""
        if isinstance(v, str):
            return [file_type.strip() for file_type in v.split(",")]
        return v
    
    def get_database_url(self, use_production: bool = False) -> str:
        """
        Get the appropriate database URL based on environment.
        
        Args:
            use_production: Whether to use production database URL
            
        Returns:
            str: Database URL
        """
        if use_production and self.DATABASE_URL_PROD:
            return self.DATABASE_URL_PROD
        elif self.DATABASE_URL:
            return self.DATABASE_URL
        else:
            # Default to SQLite for development
            return "sqlite+aiosqlite:///./dev.db"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create global settings instance
settings = Settings()
