"""
User model for authentication and user management.

This module contains the User model with all necessary fields for
authentication, user preferences, and security features.
"""

import uuid
from datetime import datetime
from typing import Optional
from sqlalchemy import String, DateTime, Inte<PERSON>, <PERSON><PERSON><PERSON>, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from sqlalchemy.sql import func

from app.core.database import Base


class User(Base):
    """
    User model for authentication and user management.
    
    Handles user accounts, authentication, preferences, and security features
    like account lockout and login tracking.
    """
    
    __tablename__ = "users"
    
    # Primary key - String UUID for compatibility
    id: Mapped[str] = mapped_column(
        String(36),
        primary_key=True,
        default=lambda: str(uuid.uuid4()),
        index=True
    )
    
    # Basic user information
    name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    email: Mapped[Optional[str]] = mapped_column(String(255), unique=True, index=True, nullable=True)
    email_verified: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    password: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    image: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # Base64 encoded image
    
    # User preferences
    language: Mapped[str] = mapped_column(String(5), default="en", nullable=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False
    )
    
    # Authentication and security
    last_login_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    login_attempts: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    locked_until: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Refresh token for OAuth2-like flow
    refresh_token_hash: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    refresh_token_expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    cvs: Mapped[list["CV"]] = relationship("CV", back_populates="user", cascade="all, delete-orphan")
    files: Mapped[list["File"]] = relationship("File", back_populates="user", cascade="all, delete-orphan")
    certificates: Mapped[list["Certificate"]] = relationship("Certificate", back_populates="user", cascade="all, delete-orphan")
    activities: Mapped[list["UserActivity"]] = relationship("UserActivity", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<User(id={self.id}, email={self.email}, name={self.name})>"
    
    @property
    def is_locked(self) -> bool:
        """Check if user account is currently locked."""
        if self.locked_until is None:
            return False
        return datetime.utcnow() < self.locked_until
    
    def reset_login_attempts(self):
        """Reset login attempts and unlock account."""
        self.login_attempts = 0
        self.locked_until = None
    
    def increment_login_attempts(self, max_attempts: int = 5, lockout_duration_minutes: int = 30):
        """
        Increment login attempts and lock account if necessary.
        
        Args:
            max_attempts: Maximum allowed login attempts
            lockout_duration_minutes: Duration to lock account in minutes
        """
        self.login_attempts += 1
        if self.login_attempts >= max_attempts:
            from datetime import timedelta
            self.locked_until = datetime.utcnow() + timedelta(minutes=lockout_duration_minutes)
