"""
Metrics and analytics service.

This module provides functionality for analyzing user activities,
generating metrics, and providing insights into application usage.
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from sqlalchemy.orm import selectinload
from loguru import logger

from app.models.user import User
from app.models.user_activity import UserActivity
from app.schemas.activity import (
    LoginFrequencyMetric, ActionFrequencyMetric, PopularActionMetric,
    ActivitySummary, UserActivityStats, MetricsResponse
)


class MetricsService:
    """
    Service for generating user activity metrics and analytics.
    
    Provides insights into user behavior, login patterns, and feature usage.
    """
    
    async def get_login_frequencies(
        self,
        db: AsyncSession,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 50
    ) -> List[LoginFrequencyMetric]:
        """
        Get login frequency metrics for users.
        
        Args:
            db: Database session
            start_date: Start date filter
            end_date: End date filter
            limit: Maximum number of results
            
        Returns:
            List[LoginFrequencyMetric]: Login frequency data
        """
        try:
            # Build query conditions
            conditions = [UserActivity.action_type == "login"]
            
            if start_date:
                conditions.append(UserActivity.timestamp >= start_date)
            if end_date:
                conditions.append(UserActivity.timestamp <= end_date)
            
            # Query login counts per user
            result = await db.execute(
                select(
                    UserActivity.user_id,
                    func.count(UserActivity.id).label('login_count'),
                    func.max(UserActivity.timestamp).label('last_login')
                )
                .where(and_(*conditions))
                .group_by(UserActivity.user_id)
                .order_by(desc('login_count'))
                .limit(limit)
            )
            
            login_data = result.all()
            
            # Get user details
            user_ids = [row.user_id for row in login_data]
            users_result = await db.execute(
                select(User).where(User.id.in_(user_ids))
            )
            users = {user.id: user for user in users_result.scalars().all()}
            
            # Build metrics
            metrics = []
            for row in login_data:
                user = users.get(row.user_id)
                metrics.append(LoginFrequencyMetric(
                    user_id=row.user_id,
                    user_name=user.name if user else None,
                    user_email=user.email if user else None,
                    login_count=row.login_count,
                    last_login=row.last_login
                ))
            
            logger.debug(f"Generated login frequency metrics for {len(metrics)} users")
            return metrics
            
        except Exception as e:
            logger.error(f"Error generating login frequency metrics: {e}")
            raise
    
    async def get_action_frequencies(
        self,
        db: AsyncSession,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 50
    ) -> List[ActionFrequencyMetric]:
        """
        Get action frequency metrics for users.
        
        Args:
            db: Database session
            start_date: Start date filter
            end_date: End date filter
            limit: Maximum number of results
            
        Returns:
            List[ActionFrequencyMetric]: Action frequency data
        """
        try:
            # Build query conditions
            conditions = []
            if start_date:
                conditions.append(UserActivity.timestamp >= start_date)
            if end_date:
                conditions.append(UserActivity.timestamp <= end_date)
            
            # Query action counts per user
            result = await db.execute(
                select(
                    UserActivity.user_id,
                    UserActivity.action_type,
                    func.count(UserActivity.id).label('action_count')
                )
                .where(and_(*conditions) if conditions else True)
                .group_by(UserActivity.user_id, UserActivity.action_type)
                .order_by(UserActivity.user_id, desc('action_count'))
            )
            
            action_data = result.all()
            
            # Group by user
            user_actions = {}
            for row in action_data:
                if row.user_id not in user_actions:
                    user_actions[row.user_id] = {}
                user_actions[row.user_id][row.action_type] = row.action_count
            
            # Get user details
            user_ids = list(user_actions.keys())
            users_result = await db.execute(
                select(User).where(User.id.in_(user_ids))
            )
            users = {user.id: user for user in users_result.scalars().all()}
            
            # Build metrics
            metrics = []
            for user_id, actions in user_actions.items():
                user = users.get(user_id)
                total_actions = sum(actions.values())
                
                metrics.append(ActionFrequencyMetric(
                    user_id=user_id,
                    user_name=user.name if user else None,
                    user_email=user.email if user else None,
                    action_counts=actions,
                    total_actions=total_actions
                ))
            
            # Sort by total actions and limit
            metrics.sort(key=lambda x: x.total_actions, reverse=True)
            metrics = metrics[:limit]
            
            logger.debug(f"Generated action frequency metrics for {len(metrics)} users")
            return metrics
            
        except Exception as e:
            logger.error(f"Error generating action frequency metrics: {e}")
            raise
    
    async def get_popular_actions(
        self,
        db: AsyncSession,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 20
    ) -> List[PopularActionMetric]:
        """
        Get popular action metrics across all users.
        
        Args:
            db: Database session
            start_date: Start date filter
            end_date: End date filter
            limit: Maximum number of results
            
        Returns:
            List[PopularActionMetric]: Popular action data
        """
        try:
            # Build query conditions
            conditions = []
            if start_date:
                conditions.append(UserActivity.timestamp >= start_date)
            if end_date:
                conditions.append(UserActivity.timestamp <= end_date)
            
            # Query popular actions
            result = await db.execute(
                select(
                    UserActivity.action_type,
                    UserActivity.endpoint,
                    func.count(UserActivity.id).label('count'),
                    func.count(func.distinct(UserActivity.user_id)).label('unique_users')
                )
                .where(and_(*conditions) if conditions else True)
                .group_by(UserActivity.action_type, UserActivity.endpoint)
                .order_by(desc('count'))
                .limit(limit)
            )
            
            action_data = result.all()
            
            # Build metrics
            metrics = []
            for row in action_data:
                metrics.append(PopularActionMetric(
                    action_type=row.action_type,
                    endpoint=row.endpoint,
                    count=row.count,
                    unique_users=row.unique_users
                ))
            
            logger.debug(f"Generated popular action metrics for {len(metrics)} actions")
            return metrics
            
        except Exception as e:
            logger.error(f"Error generating popular action metrics: {e}")
            raise
    
    async def get_activity_summary(
        self,
        db: AsyncSession,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> ActivitySummary:
        """
        Get overall activity summary.
        
        Args:
            db: Database session
            start_date: Start date filter
            end_date: End date filter
            
        Returns:
            ActivitySummary: Activity summary data
        """
        try:
            # Build query conditions
            conditions = []
            if start_date:
                conditions.append(UserActivity.timestamp >= start_date)
            if end_date:
                conditions.append(UserActivity.timestamp <= end_date)
            
            # Get total activities
            total_result = await db.execute(
                select(func.count(UserActivity.id))
                .where(and_(*conditions) if conditions else True)
            )
            total_activities = total_result.scalar()
            
            # Get unique users
            users_result = await db.execute(
                select(func.count(func.distinct(UserActivity.user_id)))
                .where(and_(*conditions) if conditions else True)
            )
            unique_users = users_result.scalar()
            
            # Get most popular action
            popular_result = await db.execute(
                select(
                    UserActivity.action_type,
                    func.count(UserActivity.id).label('count')
                )
                .where(and_(*conditions) if conditions else True)
                .group_by(UserActivity.action_type)
                .order_by(desc('count'))
                .limit(1)
            )
            popular_action_row = popular_result.first()
            most_popular_action = popular_action_row.action_type if popular_action_row else "N/A"
            
            # Get date range
            date_range_result = await db.execute(
                select(
                    func.min(UserActivity.timestamp).label('min_date'),
                    func.max(UserActivity.timestamp).label('max_date')
                )
                .where(and_(*conditions) if conditions else True)
            )
            date_range_row = date_range_result.first()
            
            date_range = {
                "start": date_range_row.min_date or datetime.utcnow(),
                "end": date_range_row.max_date or datetime.utcnow()
            }
            
            summary = ActivitySummary(
                total_activities=total_activities or 0,
                unique_users=unique_users or 0,
                most_popular_action=most_popular_action,
                date_range=date_range
            )
            
            logger.debug("Generated activity summary")
            return summary
            
        except Exception as e:
            logger.error(f"Error generating activity summary: {e}")
            raise
    
    async def get_user_activity_stats(
        self,
        db: AsyncSession,
        user_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> UserActivityStats:
        """
        Get activity statistics for a specific user.
        
        Args:
            db: Database session
            user_id: User ID
            start_date: Start date filter
            end_date: End date filter
            
        Returns:
            UserActivityStats: User activity statistics
        """
        try:
            # Build query conditions
            conditions = [UserActivity.user_id == user_id]
            if start_date:
                conditions.append(UserActivity.timestamp >= start_date)
            if end_date:
                conditions.append(UserActivity.timestamp <= end_date)
            
            # Get total activities
            total_result = await db.execute(
                select(func.count(UserActivity.id))
                .where(and_(*conditions))
            )
            total_activities = total_result.scalar() or 0
            
            # Get first and last activity
            date_result = await db.execute(
                select(
                    func.min(UserActivity.timestamp).label('first_activity'),
                    func.max(UserActivity.timestamp).label('last_activity')
                )
                .where(and_(*conditions))
            )
            date_row = date_result.first()
            
            # Get action breakdown
            action_result = await db.execute(
                select(
                    UserActivity.action_type,
                    func.count(UserActivity.id).label('count')
                )
                .where(and_(*conditions))
                .group_by(UserActivity.action_type)
                .order_by(desc('count'))
            )
            
            action_breakdown = {row.action_type: row.count for row in action_result.all()}
            
            # Get most used endpoints
            endpoint_result = await db.execute(
                select(
                    UserActivity.endpoint,
                    func.count(UserActivity.id).label('count')
                )
                .where(and_(*conditions))
                .filter(UserActivity.endpoint.isnot(None))
                .group_by(UserActivity.endpoint)
                .order_by(desc('count'))
                .limit(10)
            )
            
            most_used_endpoints = [
                {"endpoint": row.endpoint, "count": row.count}
                for row in endpoint_result.all()
            ]
            
            stats = UserActivityStats(
                user_id=user_id,
                total_activities=total_activities,
                first_activity=date_row.first_activity if date_row else None,
                last_activity=date_row.last_activity if date_row else None,
                action_breakdown=action_breakdown,
                most_used_endpoints=most_used_endpoints
            )
            
            logger.debug(f"Generated activity stats for user {user_id}")
            return stats
            
        except Exception as e:
            logger.error(f"Error generating user activity stats: {e}")
            raise
