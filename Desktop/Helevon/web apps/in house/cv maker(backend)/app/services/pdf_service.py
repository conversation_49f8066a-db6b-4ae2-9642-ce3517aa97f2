"""
PDF generation service.

This module provides PDF generation functionality for CVs with
template support, certificate merging, and multilingual capabilities.
"""

import base64
import io
from typing import Optional, List, Dict, Any
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_RIGHT
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from loguru import logger

from app.models.cv import CV
from app.models.file import File as FileModel
from app.core.config import settings


class PDFService:
    """
    Service for generating PDF documents from CV data.
    
    Supports multiple templates, multilingual content, and certificate merging.
    """
    
    def __init__(self):
        """Initialize PDF service with styles and configurations."""
        self.styles = getSampleStyleSheet()
        self.setup_custom_styles()
    
    def setup_custom_styles(self):
        """Setup custom paragraph styles for different CV sections."""
        # Header style
        self.styles.add(ParagraphStyle(
            name='CVHeader',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=12,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        ))
        
        # Section header style
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=6,
            spaceBefore=12,
            textColor=colors.darkblue,
            borderWidth=1,
            borderColor=colors.darkblue,
            borderPadding=3
        ))
        
        # Contact info style
        self.styles.add(ParagraphStyle(
            name='ContactInfo',
            parent=self.styles['Normal'],
            fontSize=10,
            alignment=TA_CENTER,
            spaceAfter=6
        ))
        
        # Entry title style
        self.styles.add(ParagraphStyle(
            name='EntryTitle',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceBefore=6,
            spaceAfter=3,
            textColor=colors.black,
            fontName='Helvetica-Bold'
        ))
        
        # Entry details style
        self.styles.add(ParagraphStyle(
            name='EntryDetails',
            parent=self.styles['Normal'],
            fontSize=10,
            spaceAfter=6,
            leftIndent=20
        ))
    
    async def generate_cv_pdf(self, cv: CV, db: AsyncSession) -> bytes:
        """
        Generate PDF for a CV.
        
        Args:
            cv: CV model instance
            db: Database session
            
        Returns:
            bytes: PDF file data
        """
        try:
            # Create PDF buffer
            buffer = io.BytesIO()
            
            # Create document
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # Build content based on template
            story = []
            
            if cv.template == "german-ausbildung":
                story = await self._build_german_ausbildung_template(cv, db)
            elif cv.template == "modern":
                story = await self._build_modern_template(cv, db)
            elif cv.template == "creative":
                story = await self._build_creative_template(cv, db)
            else:  # standard template
                story = await self._build_standard_template(cv, db)
            
            # Build PDF
            doc.build(story)
            
            # Get PDF data
            pdf_data = buffer.getvalue()
            buffer.close()
            
            logger.info(f"PDF generated for CV {cv.id}, template: {cv.template}")
            return pdf_data
            
        except Exception as e:
            logger.error(f"PDF generation failed for CV {cv.id}: {e}")
            raise
    
    async def _build_standard_template(self, cv: CV, db: AsyncSession) -> List:
        """Build standard CV template."""
        story = []
        
        # Header with personal info
        personal_info = cv.personal_info or {}
        
        # Name header
        name = f"{personal_info.get('firstName', '')} {personal_info.get('lastName', '')}".strip()
        if name:
            story.append(Paragraph(name, self.styles['CVHeader']))
        
        # Contact information
        contact_parts = []
        if personal_info.get('email'):
            contact_parts.append(personal_info['email'])
        if personal_info.get('phone'):
            contact_parts.append(personal_info['phone'])
        if personal_info.get('address'):
            address = f"{personal_info['address']}, {personal_info.get('city', '')}, {personal_info.get('country', '')}".strip(', ')
            contact_parts.append(address)
        
        if contact_parts:
            story.append(Paragraph(" | ".join(contact_parts), self.styles['ContactInfo']))
        
        story.append(Spacer(1, 12))
        
        # Add photo if available
        await self._add_photo_to_story(story, cv, db)
        
        # Work Experience
        if cv.work_experience:
            story.append(Paragraph(self._get_section_title("Work Experience", cv.language), self.styles['SectionHeader']))
            for exp in cv.work_experience:
                story.extend(self._format_work_experience_entry(exp))
        
        # Education
        if cv.education:
            story.append(Paragraph(self._get_section_title("Education", cv.language), self.styles['SectionHeader']))
            for edu in cv.education:
                story.extend(self._format_education_entry(edu))
        
        # Skills
        if cv.skills:
            story.append(Paragraph(self._get_section_title("Skills", cv.language), self.styles['SectionHeader']))
            story.extend(self._format_skills_section(cv.skills))
        
        # References
        if cv.references:
            story.append(Paragraph(self._get_section_title("References", cv.language), self.styles['SectionHeader']))
            for ref in cv.references:
                story.extend(self._format_reference_entry(ref))
        
        return story
    
    async def _build_modern_template(self, cv: CV, db: AsyncSession) -> List:
        """Build modern CV template with enhanced styling."""
        # For now, use standard template with modern styling
        # This can be enhanced with more sophisticated layouts
        return await self._build_standard_template(cv, db)
    
    async def _build_creative_template(self, cv: CV, db: AsyncSession) -> List:
        """Build creative CV template with unique design elements."""
        # For now, use standard template with creative styling
        # This can be enhanced with colors, graphics, etc.
        return await self._build_standard_template(cv, db)
    
    async def _build_german_ausbildung_template(self, cv: CV, db: AsyncSession) -> List:
        """Build German Ausbildung-specific CV template."""
        story = []
        
        # German CV typically starts with personal details
        personal_info = cv.personal_info or {}
        
        # Header
        story.append(Paragraph("Lebenslauf", self.styles['CVHeader']))
        story.append(Spacer(1, 12))
        
        # Personal data table (German style)
        personal_data = [
            ["Name:", f"{personal_info.get('firstName', '')} {personal_info.get('lastName', '')}".strip()],
            ["Geburtsdatum:", personal_info.get('dateOfBirth', '')],
            ["Geburtsort:", personal_info.get('placeOfBirth', '')],
            ["Nationalität:", personal_info.get('nationality', '')],
            ["Familienstand:", personal_info.get('maritalStatus', '')],
            ["Adresse:", f"{personal_info.get('address', '')}, {personal_info.get('postalCode', '')} {personal_info.get('city', '')}".strip(', ')],
            ["Telefon:", personal_info.get('phone', '')],
            ["E-Mail:", personal_info.get('email', '')]
        ]
        
        # Filter out empty rows
        personal_data = [[label, value] for label, value in personal_data if value]
        
        if personal_data:
            table = Table(personal_data, colWidths=[2*inch, 4*inch])
            table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            story.append(table)
            story.append(Spacer(1, 12))
        
        # Continue with standard sections but in German
        if cv.education:
            story.append(Paragraph("Bildungsweg", self.styles['SectionHeader']))
            for edu in cv.education:
                story.extend(self._format_education_entry(edu))
        
        if cv.work_experience:
            story.append(Paragraph("Berufserfahrung", self.styles['SectionHeader']))
            for exp in cv.work_experience:
                story.extend(self._format_work_experience_entry(exp))
        
        if cv.skills:
            story.append(Paragraph("Kenntnisse und Fähigkeiten", self.styles['SectionHeader']))
            story.extend(self._format_skills_section(cv.skills))
        
        return story

    async def _add_photo_to_story(self, story: List, cv: CV, db: AsyncSession):
        """Add photo to PDF story if available."""
        try:
            # Get photo file
            result = await db.execute(
                select(FileModel).where(
                    FileModel.cv_id == cv.id,
                    FileModel.category == "photo"
                )
            )
            photo_file = result.scalar_one_or_none()

            if photo_file and photo_file.file_data:
                # Decode base64 image
                image_data = base64.b64decode(photo_file.file_data)
                image_buffer = io.BytesIO(image_data)

                # Create image
                img = Image(image_buffer, width=1.5*inch, height=2*inch)
                story.append(img)
                story.append(Spacer(1, 12))

        except Exception as e:
            logger.warning(f"Failed to add photo to PDF: {e}")

    def _get_section_title(self, title: str, language: str) -> str:
        """Get localized section title."""
        translations = {
            "en": {
                "Work Experience": "Work Experience",
                "Education": "Education",
                "Skills": "Skills",
                "References": "References"
            },
            "de": {
                "Work Experience": "Berufserfahrung",
                "Education": "Bildungsweg",
                "Skills": "Kenntnisse und Fähigkeiten",
                "References": "Referenzen"
            },
            "ar": {
                "Work Experience": "الخبرة المهنية",
                "Education": "التعليم",
                "Skills": "المهارات",
                "References": "المراجع"
            }
        }

        return translations.get(language, translations["en"]).get(title, title)

    def _format_work_experience_entry(self, exp: Dict[str, Any]) -> List:
        """Format work experience entry."""
        story = []

        # Job title and company
        title_text = f"<b>{exp.get('position', '')}</b> at {exp.get('company', '')}"
        if exp.get('location'):
            title_text += f" ({exp['location']})"
        story.append(Paragraph(title_text, self.styles['EntryTitle']))

        # Dates
        start_date = exp.get('startDate', '')
        end_date = exp.get('endDate', '') if not exp.get('isCurrentlyWorking') else 'Present'
        if start_date:
            date_text = f"{start_date} - {end_date}"
            story.append(Paragraph(date_text, self.styles['EntryDetails']))

        # Description
        if exp.get('description'):
            story.append(Paragraph(exp['description'], self.styles['EntryDetails']))

        story.append(Spacer(1, 6))
        return story

    def _format_education_entry(self, edu: Dict[str, Any]) -> List:
        """Format education entry."""
        story = []

        # Degree and institution
        title_text = f"<b>{edu.get('degree', '')}</b> in {edu.get('fieldOfStudy', '')}"
        story.append(Paragraph(title_text, self.styles['EntryTitle']))

        institution_text = edu.get('institution', '')
        if institution_text:
            story.append(Paragraph(institution_text, self.styles['EntryDetails']))

        # Dates
        start_date = edu.get('startDate', '')
        end_date = edu.get('endDate', '') if not edu.get('isCurrentlyStudying') else 'Present'
        if start_date:
            date_text = f"{start_date} - {end_date}"
            story.append(Paragraph(date_text, self.styles['EntryDetails']))

        # Grade
        if edu.get('grade'):
            story.append(Paragraph(f"Grade: {edu['grade']}", self.styles['EntryDetails']))

        # Description
        if edu.get('description'):
            story.append(Paragraph(edu['description'], self.styles['EntryDetails']))

        story.append(Spacer(1, 6))
        return story

    def _format_skills_section(self, skills: List[Dict[str, Any]]) -> List:
        """Format skills section."""
        story = []

        # Group skills by category
        skill_groups = {}
        for skill in skills:
            category = skill.get('category', 'other')
            if category not in skill_groups:
                skill_groups[category] = []
            skill_groups[category].append(skill)

        # Format each category
        for category, category_skills in skill_groups.items():
            category_title = category.replace('_', ' ').title()
            story.append(Paragraph(f"<b>{category_title}:</b>", self.styles['EntryTitle']))

            skill_items = []
            for skill in category_skills:
                skill_text = skill.get('name', '')
                if skill.get('level'):
                    skill_text += f" ({skill['level']})"
                skill_items.append(skill_text)

            if skill_items:
                story.append(Paragraph(", ".join(skill_items), self.styles['EntryDetails']))

            story.append(Spacer(1, 6))

        return story

    def _format_reference_entry(self, ref: Dict[str, Any]) -> List:
        """Format reference entry."""
        story = []

        # Name and position
        title_text = f"<b>{ref.get('name', '')}</b>"
        if ref.get('position'):
            title_text += f", {ref['position']}"
        story.append(Paragraph(title_text, self.styles['EntryTitle']))

        # Company
        if ref.get('company'):
            story.append(Paragraph(ref['company'], self.styles['EntryDetails']))

        # Contact info
        contact_parts = []
        if ref.get('email'):
            contact_parts.append(f"Email: {ref['email']}")
        if ref.get('phone'):
            contact_parts.append(f"Phone: {ref['phone']}")

        if contact_parts:
            story.append(Paragraph(" | ".join(contact_parts), self.styles['EntryDetails']))

        story.append(Spacer(1, 6))
        return story
