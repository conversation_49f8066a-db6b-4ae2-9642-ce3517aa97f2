"""
File management endpoints.

This module contains all file-related API endpoints including
file upload, retrieval, and management operations.
"""

import base64
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from fastapi.responses import Response
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from loguru import logger

from app.core.database import get_db
from app.core.config import settings
from app.core.dependencies import get_current_user, require_user_ownership
from app.models.user import User
from app.models.cv import CV
from app.models.file import File as FileModel
from app.models.user_activity import UserActivity
from app.schemas.file import (
    FileResponse, FileWithData, FileListResponse, FileDeleteResponse
)
from app.schemas.base import SuccessResponse


router = APIRouter()


@router.post("/cv/{cv_id}/upload", response_model=FileResponse, status_code=status.HTTP_201_CREATED)
async def upload_file(
    cv_id: str,
    file: UploadFile = File(...),
    category: str = Form(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Upload a file associated with a CV.
    
    Args:
        cv_id: CV unique identifier
        file: File to upload
        category: File category (photo, certificate, cover_letter, other)
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        FileResponse: Uploaded file information
        
    Raises:
        HTTPException: If upload fails or validation errors occur
    """
    try:
        # Validate category
        valid_categories = ["photo", "certificate", "cover_letter", "other"]
        if category not in valid_categories:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid category. Must be one of: {', '.join(valid_categories)}"
            )
        
        # Get CV and verify ownership
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()
        
        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )
        
        require_user_ownership(cv.user_id, current_user)
        
        # Validate file type
        if file.content_type not in settings.ALLOWED_FILE_TYPES:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type not allowed. Supported types: {', '.join(settings.ALLOWED_FILE_TYPES)}"
            )
        
        # Read file content
        file_content = await file.read()
        file_size = len(file_content)
        
        # Validate file size
        max_size = settings.MAX_UPLOAD_SIZE_MB * 1024 * 1024
        if file_size > max_size:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File too large. Maximum size: {settings.MAX_UPLOAD_SIZE_MB}MB"
            )
        
        # Encode file as base64
        file_data = base64.b64encode(file_content).decode('utf-8')
        
        # Handle single photo per CV constraint
        if category == "photo":
            # Delete existing photo for this CV
            existing_photo = await db.execute(
                select(FileModel).where(
                    FileModel.cv_id == cv_id,
                    FileModel.category == "photo"
                )
            )
            existing = existing_photo.scalar_one_or_none()
            if existing:
                await db.delete(existing)
        
        # Create file record
        new_file = FileModel(
            user_id=current_user.id,
            cv_id=cv_id,
            name=file.filename,
            type=file.content_type,
            size=file_size,
            file_data=file_data,
            category=category,
            url=""  # Will be set after creation
        )
        
        db.add(new_file)
        await db.commit()
        await db.refresh(new_file)
        
        # Generate URL
        new_file.url = new_file.generate_url()
        await db.commit()
        await db.refresh(new_file)
        
        # Log file upload activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="upload_file",
            endpoint=f"/api/v1/cv/{cv_id}/upload",
            details={
                "file_id": new_file.id,
                "cv_id": cv_id,
                "filename": file.filename,
                "category": category,
                "size": file_size
            }
        )
        db.add(activity)
        await db.commit()
        
        logger.info(f"File uploaded: {new_file.id} for CV {cv_id} by user {current_user.email}")
        return FileResponse.model_validate(new_file)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File upload error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="File upload failed"
        )


@router.get("/cv/{cv_id}/file/{file_id}")
async def get_file(
    cv_id: str,
    file_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Retrieve a file by ID.
    
    Args:
        cv_id: CV unique identifier
        file_id: File unique identifier
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Response: Binary file data with appropriate Content-Type header
        
    Raises:
        HTTPException: If file not found or access denied
    """
    try:
        # Get file
        result = await db.execute(
            select(FileModel).where(
                FileModel.id == file_id,
                FileModel.cv_id == cv_id
            )
        )
        file_record = result.scalar_one_or_none()
        
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        # Check ownership
        require_user_ownership(file_record.user_id, current_user)
        
        # Decode base64 file data
        file_data = base64.b64decode(file_record.file_data)
        
        logger.debug(f"File retrieved: {file_id} by user {current_user.email}")
        
        return Response(
            content=file_data,
            media_type=file_record.type,
            headers={
                "Content-Disposition": f"inline; filename={file_record.name}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File retrieval error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve file"
        )


@router.get("/cv/{cv_id}/files", response_model=List[FileListResponse])
async def get_cv_files(
    cv_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all files associated with a CV.
    
    Args:
        cv_id: CV unique identifier
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[FileListResponse]: List of files
    """
    try:
        # Verify CV ownership
        result = await db.execute(select(CV).where(CV.id == cv_id))
        cv = result.scalar_one_or_none()
        
        if not cv:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="CV not found"
            )
        
        require_user_ownership(cv.user_id, current_user)
        
        # Get files
        files_result = await db.execute(
            select(FileModel).where(FileModel.cv_id == cv_id).order_by(FileModel.created_at.desc())
        )
        files = files_result.scalars().all()
        
        logger.debug(f"Retrieved {len(files)} files for CV {cv_id}")
        
        return [
            FileListResponse(
                id=file.id,
                name=file.name,
                type=file.type,
                size=file.size,
                category=file.category,
                url=file.url,
                created_at=file.created_at.isoformat()
            )
            for file in files
        ]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving CV files: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve files"
        )


@router.delete("/cv/{cv_id}/file/{file_id}", response_model=FileDeleteResponse)
async def delete_file(
    cv_id: str,
    file_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a file.

    Args:
        cv_id: CV unique identifier
        file_id: File unique identifier
        current_user: Current authenticated user
        db: Database session

    Returns:
        FileDeleteResponse: Deletion confirmation

    Raises:
        HTTPException: If file not found or access denied
    """
    try:
        # Get file
        result = await db.execute(
            select(FileModel).where(
                FileModel.id == file_id,
                FileModel.cv_id == cv_id
            )
        )
        file_record = result.scalar_one_or_none()

        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        # Check ownership
        require_user_ownership(file_record.user_id, current_user)

        file_name = file_record.name

        # Delete file
        await db.delete(file_record)
        await db.commit()

        # Log file deletion activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="delete_file",
            endpoint=f"/api/v1/cv/{cv_id}/file/{file_id}",
            details={
                "file_id": file_id,
                "cv_id": cv_id,
                "filename": file_name
            }
        )
        db.add(activity)
        await db.commit()

        logger.info(f"File deleted: {file_id} by user {current_user.email}")
        return FileDeleteResponse(
            message="File deleted successfully",
            file_id=file_id
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File deletion error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete file"
        )


@router.get("/file/{file_id}")
async def get_standalone_file(
    file_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Retrieve a standalone file by ID (not associated with CV).

    Args:
        file_id: File unique identifier
        current_user: Current authenticated user
        db: Database session

    Returns:
        Response: Binary file data with appropriate Content-Type header
    """
    try:
        # Get file
        result = await db.execute(
            select(FileModel).where(
                FileModel.id == file_id,
                FileModel.cv_id.is_(None)
            )
        )
        file_record = result.scalar_one_or_none()

        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        # Check ownership
        require_user_ownership(file_record.user_id, current_user)

        # Decode base64 file data
        file_data = base64.b64decode(file_record.file_data)

        logger.debug(f"Standalone file retrieved: {file_id} by user {current_user.email}")

        return Response(
            content=file_data,
            media_type=file_record.type,
            headers={
                "Content-Disposition": f"inline; filename={file_record.name}"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Standalone file retrieval error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve file"
        )
