"""
Authentication endpoints.

This module contains all authentication-related API endpoints including
login, logout, registration, token refresh, and password verification.
"""

from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from loguru import logger

from app.core.database import get_db
from app.core.auth import auth_manager, get_password_hash, verify_password
from app.core.dependencies import validate_refresh_token, get_current_user
from app.models.user import User
from app.models.user_activity import UserActivity
from app.schemas.user import (
    UserCreate, UserLogin, UserResponse, TokenResponse, 
    RefreshTokenRequest, PasswordVerification, PasswordVerificationResponse
)
from app.schemas.base import SuccessResponse, ErrorResponse


router = APIRouter()


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Register a new user account.
    
    Args:
        user_data: User registration data
        db: Database session
        
    Returns:
        UserResponse: Created user data
        
    Raises:
        HTTPException: If email already exists or registration fails
    """
    try:
        # Check if user already exists
        result = await db.execute(
            select(User).where(User.email == user_data.email)
        )
        existing_user = result.scalar_one_or_none()
        
        if existing_user:
            logger.warning(f"Registration attempt with existing email: {user_data.email}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Create new user
        hashed_password = get_password_hash(user_data.password)
        new_user = User(
            name=user_data.name,
            email=user_data.email,
            password=hashed_password,
            language=user_data.language
        )
        
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        
        # Log registration activity
        activity = UserActivity.create_activity(
            user_id=new_user.id,
            action_type="register",
            endpoint="/api/v1/auth/register",
            details={"email": user_data.email, "name": user_data.name}
        )
        db.add(activity)
        await db.commit()
        
        logger.info(f"New user registered: {new_user.email}")
        return UserResponse.model_validate(new_user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"User registration error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/signin", response_model=TokenResponse)
async def login_user(
    login_data: UserLogin,
    db: AsyncSession = Depends(get_db)
):
    """
    Authenticate user and return access/refresh tokens.
    
    Args:
        login_data: User login credentials
        db: Database session
        
    Returns:
        TokenResponse: JWT tokens and metadata
        
    Raises:
        HTTPException: If credentials are invalid or account is locked
    """
    try:
        # Get user by email
        result = await db.execute(
            select(User).where(User.email == login_data.email)
        )
        user = result.scalar_one_or_none()
        
        if not user:
            logger.warning(f"Login attempt with non-existent email: {login_data.email}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        
        # Check if account is locked
        if user.is_locked:
            logger.warning(f"Login attempt on locked account: {user.email}")
            raise HTTPException(
                status_code=status.HTTP_423_LOCKED,
                detail="Account is temporarily locked due to too many failed login attempts"
            )
        
        # Verify password
        if not verify_password(login_data.password, user.password):
            # Increment login attempts
            user.increment_login_attempts()
            await db.commit()
            
            logger.warning(f"Failed login attempt for: {user.email}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        
        # Reset login attempts on successful login
        user.reset_login_attempts()
        user.last_login_at = datetime.utcnow()
        
        # Create token pair
        token_data = auth_manager.create_token_pair(user.id, user.email)
        
        # Store refresh token hash
        user.refresh_token_hash = token_data["refresh_token_hash"]
        user.refresh_token_expires_at = token_data["refresh_expires_at"]
        
        await db.commit()
        
        # Log login activity
        activity = UserActivity.create_activity(
            user_id=user.id,
            action_type="login",
            endpoint="/api/v1/auth/signin",
            details={"email": user.email}
        )
        db.add(activity)
        await db.commit()
        
        logger.info(f"User logged in: {user.email}")
        
        return TokenResponse(
            access_token=token_data["access_token"],
            refresh_token=token_data["refresh_token"],
            token_type=token_data["token_type"],
            expires_in=token_data["expires_in"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/refresh-token", response_model=TokenResponse)
async def refresh_access_token(
    refresh_data: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Refresh access token using refresh token.
    
    Args:
        refresh_data: Refresh token request
        db: Database session
        
    Returns:
        TokenResponse: New access token
        
    Raises:
        HTTPException: If refresh token is invalid
    """
    try:
        # Validate refresh token and get user
        user = await validate_refresh_token(refresh_data.refresh_token, db)
        
        # Create new access token
        token_data = auth_manager.create_token_pair(user.id, user.email)
        
        # Update stored refresh token hash
        user.refresh_token_hash = token_data["refresh_token_hash"]
        user.refresh_token_expires_at = token_data["refresh_expires_at"]
        
        await db.commit()
        
        logger.info(f"Token refreshed for user: {user.email}")
        
        return TokenResponse(
            access_token=token_data["access_token"],
            refresh_token=token_data["refresh_token"],
            token_type=token_data["token_type"],
            expires_in=token_data["expires_in"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.post("/signout", response_model=SuccessResponse)
async def logout_user(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Logout user by invalidating refresh token.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SuccessResponse: Logout confirmation
    """
    try:
        # Invalidate refresh token
        current_user.refresh_token_hash = None
        current_user.refresh_token_expires_at = None
        
        await db.commit()
        
        # Log logout activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="logout",
            endpoint="/api/v1/auth/signout",
            details={"email": current_user.email}
        )
        db.add(activity)
        await db.commit()
        
        logger.info(f"User logged out: {current_user.email}")
        
        return SuccessResponse(message="Successfully logged out")
        
    except Exception as e:
        logger.error(f"Logout error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.post("/verify-password", response_model=PasswordVerificationResponse)
async def verify_user_password(password_data: PasswordVerification):
    """
    Verify password against hashed password.
    
    Args:
        password_data: Password verification data
        
    Returns:
        PasswordVerificationResponse: Verification result
    """
    try:
        is_valid = verify_password(password_data.password, password_data.hashed_password)
        return PasswordVerificationResponse(is_valid=is_valid)
        
    except Exception as e:
        logger.error(f"Password verification error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password verification failed"
        )
