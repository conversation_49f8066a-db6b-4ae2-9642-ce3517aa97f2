"""
User management endpoints.

This module contains all user account management API endpoints including
account retrieval, updates, and password changes.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from loguru import logger

from app.core.database import get_db
from app.core.auth import get_password_hash, verify_password
from app.core.dependencies import get_current_user
from app.models.user import User
from app.models.user_activity import UserActivity
from app.schemas.user import UserResponse, UserUpdate, UserProfile
from app.schemas.base import SuccessResponse


router = APIRouter()


@router.get("/account", response_model=UserResponse)
async def get_user_account(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user account information.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        UserResponse: User account information
    """
    try:
        logger.debug(f"Retrieving account info for user: {current_user.email}")
        return UserResponse.model_validate(current_user)
        
    except Exception as e:
        logger.error(f"Error retrieving user account: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve account information"
        )


@router.get("/profile", response_model=UserProfile)
async def get_user_profile(
    current_user: User = Depends(get_current_user)
):
    """
    Get detailed user profile information.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        UserProfile: Detailed user profile
    """
    try:
        logger.debug(f"Retrieving profile for user: {current_user.email}")
        return UserProfile.model_validate(current_user)
        
    except Exception as e:
        logger.error(f"Error retrieving user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve profile information"
        )


@router.put("/account", response_model=UserResponse)
async def update_user_account(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Update user account information.
    
    Args:
        user_update: User update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        UserResponse: Updated user information
        
    Raises:
        HTTPException: If update fails or validation errors occur
    """
    try:
        # Track what fields are being updated
        updated_fields = []
        
        # Update basic fields
        if user_update.name is not None:
            current_user.name = user_update.name
            updated_fields.append("name")
        
        if user_update.email is not None:
            # Check if email is already taken by another user
            result = await db.execute(
                select(User).where(
                    User.email == user_update.email,
                    User.id != current_user.id
                )
            )
            existing_user = result.scalar_one_or_none()
            
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already in use by another account"
                )
            
            current_user.email = user_update.email
            updated_fields.append("email")
        
        if user_update.language is not None:
            current_user.language = user_update.language
            updated_fields.append("language")
        
        # Handle password change
        if user_update.new_password is not None:
            # Verify current password
            if not user_update.current_password:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Current password is required to change password"
                )
            
            if not verify_password(user_update.current_password, current_user.password):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Current password is incorrect"
                )
            
            # Update password
            current_user.password = get_password_hash(user_update.new_password)
            updated_fields.append("password")
            
            # Invalidate refresh tokens for security
            current_user.refresh_token_hash = None
            current_user.refresh_token_expires_at = None
        
        # Save changes
        await db.commit()
        await db.refresh(current_user)
        
        # Log account update activity
        activity = UserActivity.create_activity(
            user_id=current_user.id,
            action_type="change_password" if "password" in updated_fields else "update_account",
            endpoint="/api/v1/user/account",
            details={
                "updated_fields": updated_fields,
                "email": current_user.email
            }
        )
        db.add(activity)
        await db.commit()
        
        logger.info(f"User account updated: {current_user.email}, fields: {updated_fields}")
        return UserResponse.model_validate(current_user)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Account update error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update account"
        )


@router.delete("/account", response_model=SuccessResponse)
async def delete_user_account(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete user account and all associated data.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SuccessResponse: Deletion confirmation
        
    Raises:
        HTTPException: If deletion fails
    """
    try:
        user_email = current_user.email
        
        # Delete user (cascade will handle related data)
        await db.delete(current_user)
        await db.commit()
        
        logger.info(f"User account deleted: {user_email}")
        return SuccessResponse(message="Account successfully deleted")
        
    except Exception as e:
        logger.error(f"Account deletion error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete account"
        )


@router.post("/unlock-account", response_model=SuccessResponse)
async def unlock_user_account(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Unlock user account (reset login attempts).
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SuccessResponse: Unlock confirmation
    """
    try:
        # Reset login attempts and unlock account
        current_user.reset_login_attempts()
        await db.commit()
        
        logger.info(f"Account unlocked: {current_user.email}")
        return SuccessResponse(message="Account successfully unlocked")
        
    except Exception as e:
        logger.error(f"Account unlock error: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to unlock account"
        )
