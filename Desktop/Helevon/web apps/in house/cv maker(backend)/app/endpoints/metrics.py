"""
Metrics and analytics endpoints.

This module contains API endpoints for retrieving user activity metrics,
analytics, and usage statistics.
"""

from datetime import datetime, timedelta
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger

from app.core.database import get_db
from app.core.dependencies import get_current_user
from app.models.user import User
from app.services.metrics_service import MetricsService
from app.schemas.activity import (
    LoginFrequencyMetric, ActionFrequencyMetric, PopularActionMetric,
    ActivitySummary, UserActivityStats, MetricsResponse, ActivityMetricsParams
)


router = APIRouter()


@router.get("/login-frequencies", response_model=List[LoginFrequencyMetric])
async def get_login_frequencies(
    start_date: Optional[datetime] = Query(None, description="Start date filter"),
    end_date: Optional[datetime] = Query(None, description="End date filter"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of results"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get login frequency metrics for users.
    
    Args:
        start_date: Start date filter
        end_date: End date filter
        limit: Maximum number of results
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[LoginFrequencyMetric]: Login frequency data
    """
    try:
        metrics_service = MetricsService()
        metrics = await metrics_service.get_login_frequencies(
            db=db,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )
        
        logger.info(f"Login frequency metrics requested by user {current_user.email}")
        return metrics
        
    except Exception as e:
        logger.error(f"Error retrieving login frequencies: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve login frequency metrics"
        )


@router.get("/action-frequencies", response_model=List[ActionFrequencyMetric])
async def get_action_frequencies(
    start_date: Optional[datetime] = Query(None, description="Start date filter"),
    end_date: Optional[datetime] = Query(None, description="End date filter"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of results"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get action frequency metrics for users.
    
    Args:
        start_date: Start date filter
        end_date: End date filter
        limit: Maximum number of results
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[ActionFrequencyMetric]: Action frequency data
    """
    try:
        metrics_service = MetricsService()
        metrics = await metrics_service.get_action_frequencies(
            db=db,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )
        
        logger.info(f"Action frequency metrics requested by user {current_user.email}")
        return metrics
        
    except Exception as e:
        logger.error(f"Error retrieving action frequencies: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve action frequency metrics"
        )


@router.get("/popular-actions", response_model=List[PopularActionMetric])
async def get_popular_actions(
    start_date: Optional[datetime] = Query(None, description="Start date filter"),
    end_date: Optional[datetime] = Query(None, description="End date filter"),
    limit: int = Query(20, ge=1, le=50, description="Maximum number of results"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get popular action metrics across all users.
    
    Args:
        start_date: Start date filter
        end_date: End date filter
        limit: Maximum number of results
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[PopularActionMetric]: Popular action data
    """
    try:
        metrics_service = MetricsService()
        metrics = await metrics_service.get_popular_actions(
            db=db,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )
        
        logger.info(f"Popular action metrics requested by user {current_user.email}")
        return metrics
        
    except Exception as e:
        logger.error(f"Error retrieving popular actions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve popular action metrics"
        )


@router.get("/summary", response_model=ActivitySummary)
async def get_activity_summary(
    start_date: Optional[datetime] = Query(None, description="Start date filter"),
    end_date: Optional[datetime] = Query(None, description="End date filter"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get overall activity summary.
    
    Args:
        start_date: Start date filter
        end_date: End date filter
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        ActivitySummary: Activity summary data
    """
    try:
        metrics_service = MetricsService()
        summary = await metrics_service.get_activity_summary(
            db=db,
            start_date=start_date,
            end_date=end_date
        )
        
        logger.info(f"Activity summary requested by user {current_user.email}")
        return summary
        
    except Exception as e:
        logger.error(f"Error retrieving activity summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve activity summary"
        )


@router.get("/user/{user_id}/stats", response_model=UserActivityStats)
async def get_user_activity_stats(
    user_id: str,
    start_date: Optional[datetime] = Query(None, description="Start date filter"),
    end_date: Optional[datetime] = Query(None, description="End date filter"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get activity statistics for a specific user.
    
    Args:
        user_id: User ID
        start_date: Start date filter
        end_date: End date filter
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        UserActivityStats: User activity statistics
    """
    try:
        # Users can only view their own stats unless they have admin privileges
        # For now, allow users to view only their own stats
        if user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Can only view your own activity statistics"
            )
        
        metrics_service = MetricsService()
        stats = await metrics_service.get_user_activity_stats(
            db=db,
            user_id=user_id,
            start_date=start_date,
            end_date=end_date
        )
        
        logger.info(f"User activity stats requested for user {user_id} by {current_user.email}")
        return stats
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving user activity stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user activity statistics"
        )


@router.get("/comprehensive", response_model=MetricsResponse)
async def get_comprehensive_metrics(
    start_date: Optional[datetime] = Query(None, description="Start date filter"),
    end_date: Optional[datetime] = Query(None, description="End date filter"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get comprehensive metrics including all metric types.
    
    Args:
        start_date: Start date filter
        end_date: End date filter
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        MetricsResponse: Comprehensive metrics data
    """
    try:
        metrics_service = MetricsService()
        
        # Get all metrics in parallel
        login_frequencies = await metrics_service.get_login_frequencies(
            db=db, start_date=start_date, end_date=end_date, limit=25
        )
        
        action_frequencies = await metrics_service.get_action_frequencies(
            db=db, start_date=start_date, end_date=end_date, limit=25
        )
        
        popular_actions = await metrics_service.get_popular_actions(
            db=db, start_date=start_date, end_date=end_date, limit=15
        )
        
        summary = await metrics_service.get_activity_summary(
            db=db, start_date=start_date, end_date=end_date
        )
        
        response = MetricsResponse(
            login_frequencies=login_frequencies,
            action_frequencies=action_frequencies,
            popular_actions=popular_actions,
            summary=summary
        )
        
        logger.info(f"Comprehensive metrics requested by user {current_user.email}")
        return response
        
    except Exception as e:
        logger.error(f"Error retrieving comprehensive metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve comprehensive metrics"
        )
