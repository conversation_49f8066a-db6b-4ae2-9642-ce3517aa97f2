#!/usr/bin/env python3
"""
Simple API test script to verify the CV Maker API is working correctly.
"""

import requests
import json

BASE_URL = "http://localhost:8001"

def test_health():
    """Test health endpoint."""
    print("Testing health endpoint...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_user_registration():
    """Test user registration."""
    print("Testing user registration...")
    import time
    timestamp = int(time.time())
    email = f"test{timestamp}@example.com"
    user_data = {
        "name": "Test User",
        "email": email,
        "password": "testpassword123",
        "confirm_password": "testpassword123",
        "language": "en"
    }

    response = requests.post(f"{BASE_URL}/api/v1/auth/register", json=user_data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

    if response.status_code == 201:
        return email
    return None

def test_user_login(email):
    """Test user login."""
    print("Testing user login...")
    login_data = {
        "email": email,
        "password": "testpassword123"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/signin", json=login_data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()
    
    if response.status_code == 200:
        return response.json().get("access_token")
    return None

def test_protected_endpoint(token):
    """Test protected endpoint with token."""
    print("Testing protected endpoint...")
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(f"{BASE_URL}/api/v1/user/account", headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_cv_creation(token):
    """Test CV creation."""
    print("Testing CV creation...")
    headers = {"Authorization": f"Bearer {token}"}
    cv_data = {
        "title": "My Test CV",
        "template": "standard",
        "language": "en",
        "user_id": "test-user-id"  # This will be overridden by the backend
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/cv", json=cv_data, headers=headers)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()
    
    if response.status_code == 201:
        return response.json().get("id")
    return None

def main():
    """Run all tests."""
    print("🧪 Testing CV Maker API")
    print("=" * 50)
    
    try:
        # Test health
        test_health()
        
        # Test registration
        email = test_user_registration()
        if email:
            print("✅ User registration successful")
        else:
            print("❌ User registration failed")
            return

        # Test login
        token = test_user_login(email)
        if token:
            print("✅ User login successful")
        else:
            print("❌ User login failed")
            return
        
        # Test protected endpoint
        test_protected_endpoint(token)
        print("✅ Protected endpoint access successful")
        
        # Test CV creation
        cv_id = test_cv_creation(token)
        if cv_id:
            print("✅ CV creation successful")
        else:
            print("❌ CV creation failed")
        
        print("\n🎉 All tests completed!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to API. Make sure the server is running on port 8001")
    except Exception as e:
        print(f"❌ Test failed with error: {e}")

if __name__ == "__main__":
    main()
