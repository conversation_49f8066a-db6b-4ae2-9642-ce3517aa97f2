#!/usr/bin/env python3
"""
CV Maker API Management Script.

This script provides comprehensive management commands for the CV Maker API
including development server, production server, and database operations.
"""

import os
import sys
import subprocess
from pathlib import Path
from typing import Optional, List

import typer
from loguru import logger

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.core.config import settings
from app.core.database import get_db_manager


app = typer.Typer(
    name="CV Maker API Manager",
    help="Management commands for the CV Maker API",
    add_completion=False
)


@app.command()
def runserver(
    host: str = typer.Option("0.0.0.0", "--host", "-h", help="Host to bind to"),
    port: int = typer.Option(8000, "--port", "-p", help="Port to bind to"),
    reload: bool = typer.Option(True, "--reload/--no-reload", help="Enable auto-reload"),
    log_level: str = typer.Option("info", "--log-level", help="Log level"),
):
    """
    Run the development server with SQLite database.
    
    This command is for local development only. It uses SQLite database,
    enables auto-reload, and includes debug features.
    """
    typer.echo("🚀 Starting CV Maker API Development Server...")
    typer.echo(f"📍 Server will be available at: http://{host}:{port}")
    typer.echo("📊 Using SQLite database for development")
    typer.echo("🔄 Auto-reload enabled")
    
    # Set development environment variables
    os.environ["DEBUG"] = "true"
    os.environ["DATABASE_URL"] = "sqlite+aiosqlite:///./dev.db"
    
    # Prepare uvicorn command
    cmd = [
        "uvicorn",
        "main:app",
        "--host", host,
        "--port", str(port),
        "--log-level", log_level,
    ]
    
    if reload:
        cmd.extend([
            "--reload",
            "--reload-dir", "app/",
            "--reload-exclude", "*.log",
            "--reload-exclude", "*.db",
            "--reload-exclude", "*.sqlite*",
            "--reload-exclude", "__pycache__",
            "--reload-exclude", ".pytest_cache",
            "--reload-exclude", "venv/",
            "--reload-exclude", ".venv/",
            "--reload-exclude", "node_modules/",
        ])
    
    try:
        # Run the server
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        typer.echo("\n👋 Development server stopped")
    except subprocess.CalledProcessError as e:
        typer.echo(f"❌ Failed to start development server: {e}", err=True)
        raise typer.Exit(1)


@app.command()
def runprod(
    host: str = typer.Option("0.0.0.0", "--host", "-h", help="Host to bind to"),
    port: int = typer.Option(8000, "--port", "-p", help="Port to bind to"),
    workers: Optional[int] = typer.Option(None, "--workers", "-w", help="Number of worker processes"),
    access_log: bool = typer.Option(True, "--access-log/--no-access-log", help="Enable access logging"),
    error_log: str = typer.Option("logs/gunicorn_error.log", "--error-log", help="Error log file"),
    access_log_file: str = typer.Option("logs/gunicorn_access.log", "--access-log-file", help="Access log file"),
    log_level: str = typer.Option("info", "--log-level", help="Log level"),
):
    """
    Run the production server with PostgreSQL database.
    
    This command is for staging and production environments. It uses PostgreSQL
    database, Gunicorn with multiple workers, and production-ready configurations.
    """
    typer.echo("🏭 Starting CV Maker API Production Server...")
    typer.echo(f"📍 Server will be available at: http://{host}:{port}")
    typer.echo("🐘 Using PostgreSQL database for production")
    
    # Validate production environment
    if not os.getenv("DATABASE_URL_PROD"):
        typer.echo("❌ DATABASE_URL_PROD environment variable is required for production", err=True)
        typer.echo("💡 Set it to your PostgreSQL connection string", err=True)
        raise typer.Exit(1)
    
    # Set production environment variables
    os.environ["DEBUG"] = "false"
    
    # Determine number of workers
    if workers is None:
        workers = settings.WORKERS or (os.cpu_count() * 2 + 1)
    
    # Ensure log directory exists
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Prepare gunicorn command
    cmd = [
        "gunicorn",
        "main:app",
        "--worker-class", "uvicorn.workers.UvicornWorker",
        "--bind", f"{host}:{port}",
        "--workers", str(workers),
        "--log-level", log_level,
        "--error-logfile", error_log,
        "--preload",
        "--max-requests", "1000",
        "--max-requests-jitter", "100",
        "--timeout", "30",
        "--keep-alive", "2",
    ]
    
    if access_log:
        cmd.extend(["--access-logfile", access_log_file])
    else:
        cmd.extend(["--access-logfile", "-"])
    
    typer.echo(f"👥 Starting with {workers} worker processes")
    
    try:
        # Run the server
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        typer.echo("\n👋 Production server stopped")
    except subprocess.CalledProcessError as e:
        typer.echo(f"❌ Failed to start production server: {e}", err=True)
        raise typer.Exit(1)


@app.command()
def migrate(
    production: bool = typer.Option(False, "--production", help="Use production database"),
    drop_all: bool = typer.Option(False, "--drop-all", help="Drop all tables before creating"),
):
    """
    Run database migrations and create tables.
    
    This command creates all database tables based on the SQLAlchemy models.
    Use --production flag to run against production database.
    """
    import asyncio
    from app.core.database import get_db_manager
    
    typer.echo("🗄️  Running database migrations...")
    
    if production:
        if not os.getenv("DATABASE_URL_PROD"):
            typer.echo("❌ DATABASE_URL_PROD environment variable is required", err=True)
            raise typer.Exit(1)
        typer.echo("🐘 Using production PostgreSQL database")
        db_manager = get_db_manager(use_production=True)
    else:
        typer.echo("📊 Using development SQLite database")
        db_manager = get_db_manager(use_production=False)
    
    async def run_migrations():
        try:
            if drop_all:
                typer.echo("⚠️  Dropping all existing tables...")
                await db_manager.drop_tables()
            
            typer.echo("📋 Creating database tables...")
            await db_manager.create_tables()
            
            typer.echo("✅ Database migration completed successfully")
            
        except Exception as e:
            typer.echo(f"❌ Migration failed: {e}", err=True)
            raise typer.Exit(1)
        finally:
            await db_manager.close()
    
    # Run the migration
    asyncio.run(run_migrations())


@app.command()
def shell():
    """
    Start an interactive Python shell with application context.
    
    This provides access to all models, services, and utilities for debugging
    and manual operations.
    """
    import asyncio
    from app.models import User, CV, File, Certificate, UserActivity
    from app.core.database import get_db
    from app.services.metrics_service import MetricsService
    from app.services.pdf_service import PDFService
    
    typer.echo("🐍 Starting interactive shell with CV Maker API context...")
    typer.echo("📦 Available imports:")
    typer.echo("   - Models: User, CV, File, Certificate, UserActivity")
    typer.echo("   - Database: get_db")
    typer.echo("   - Services: MetricsService, PDFService")
    
    # Start IPython if available, otherwise use standard Python shell
    try:
        import IPython
        IPython.start_ipython(argv=[], user_ns=locals())
    except ImportError:
        import code
        code.interact(local=locals())


@app.command()
def test(
    coverage: bool = typer.Option(False, "--coverage", help="Run with coverage report"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Verbose output"),
    pattern: Optional[str] = typer.Option(None, "--pattern", "-k", help="Test pattern to match"),
):
    """
    Run the test suite.
    
    This command runs all tests using pytest with optional coverage reporting.
    """
    typer.echo("🧪 Running test suite...")
    
    cmd = ["pytest"]
    
    if verbose:
        cmd.append("-v")
    
    if pattern:
        cmd.extend(["-k", pattern])
    
    if coverage:
        cmd.extend([
            "--cov=app",
            "--cov-report=html",
            "--cov-report=term-missing"
        ])
        typer.echo("📊 Coverage report will be generated")
    
    try:
        result = subprocess.run(cmd, check=False)
        if result.returncode == 0:
            typer.echo("✅ All tests passed!")
        else:
            typer.echo("❌ Some tests failed", err=True)
            raise typer.Exit(result.returncode)
    except FileNotFoundError:
        typer.echo("❌ pytest not found. Install it with: pip install pytest", err=True)
        raise typer.Exit(1)


@app.command()
def logs(
    follow: bool = typer.Option(False, "--follow", "-f", help="Follow log output"),
    lines: int = typer.Option(50, "--lines", "-n", help="Number of lines to show"),
    log_file: str = typer.Option("app.log", "--file", help="Log file to view"),
):
    """
    View application logs.
    
    This command displays recent log entries from the specified log file.
    """
    log_path = Path("logs") / log_file
    
    if not log_path.exists():
        typer.echo(f"❌ Log file not found: {log_path}", err=True)
        raise typer.Exit(1)
    
    if follow:
        cmd = ["tail", "-f", str(log_path)]
    else:
        cmd = ["tail", "-n", str(lines), str(log_path)]
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        typer.echo("\n👋 Stopped following logs")
    except subprocess.CalledProcessError as e:
        typer.echo(f"❌ Failed to read logs: {e}", err=True)
        raise typer.Exit(1)


@app.command()
def version():
    """Show application version and environment information."""
    typer.echo("🏷️  CV Maker API Version Information")
    typer.echo("=" * 40)
    typer.echo(f"Version: 1.0.0")
    typer.echo(f"Debug Mode: {settings.DEBUG}")
    typer.echo(f"Database URL: {settings.get_database_url()}")
    typer.echo(f"Host: {settings.HOST}")
    typer.echo(f"Port: {settings.PORT}")
    typer.echo(f"Workers: {settings.WORKERS}")
    typer.echo(f"Max Upload Size: {settings.MAX_UPLOAD_SIZE_MB}MB")
    typer.echo(f"PDF Export Enabled: {settings.CAN_EXPORT}")


if __name__ == "__main__":
    app()
