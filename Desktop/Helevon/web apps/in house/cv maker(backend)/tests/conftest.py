"""
Test configuration and fixtures.

This module provides pytest fixtures and configuration for testing
the CV Maker API with test database and authentication.
"""

import asyncio
import pytest
import pytest_asyncio
from typing import AsyncGenerator, Generator
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool

from main import app
from app.core.database import get_db, Base
from app.core.auth import auth_manager
from app.models.user import User
from app.models.cv import CV


# Test database URL (in-memory SQLite)
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="function")
async def test_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Create a test database session.
    
    Yields:
        AsyncSession: Test database session
    """
    # Create test engine
    engine = create_async_engine(
        TEST_DATABASE_URL,
        connect_args={
            "check_same_thread": False,
        },
        poolclass=StaticPool,
        echo=False
    )
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Create session
    async_session = async_sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
    
    # Clean up
    await engine.dispose()


@pytest.fixture
def client(test_db: AsyncSession) -> TestClient:
    """
    Create a test client with database dependency override.
    
    Args:
        test_db: Test database session
        
    Returns:
        TestClient: FastAPI test client
    """
    async def override_get_db():
        yield test_db
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
    
    # Clean up
    app.dependency_overrides.clear()


@pytest_asyncio.fixture
async def test_user(test_db: AsyncSession) -> User:
    """
    Create a test user.
    
    Args:
        test_db: Test database session
        
    Returns:
        User: Test user instance
    """
    user = User(
        name="Test User",
        email="<EMAIL>",
        password=auth_manager.hash_password("testpassword123"),
        language="en"
    )
    
    test_db.add(user)
    await test_db.commit()
    await test_db.refresh(user)
    
    return user


@pytest_asyncio.fixture
async def test_user_token(test_user: User) -> str:
    """
    Create an access token for the test user.
    
    Args:
        test_user: Test user instance
        
    Returns:
        str: JWT access token
    """
    token_data = {"sub": test_user.id, "email": test_user.email}
    return auth_manager.create_access_token(token_data)


@pytest_asyncio.fixture
async def authenticated_headers(test_user_token: str) -> dict:
    """
    Create authentication headers for API requests.
    
    Args:
        test_user_token: JWT access token
        
    Returns:
        dict: Authentication headers
    """
    return {"Authorization": f"Bearer {test_user_token}"}


@pytest_asyncio.fixture
async def test_cv(test_db: AsyncSession, test_user: User) -> CV:
    """
    Create a test CV.
    
    Args:
        test_db: Test database session
        test_user: Test user instance
        
    Returns:
        CV: Test CV instance
    """
    cv = CV(
        user_id=test_user.id,
        title="Test CV",
        template="standard",
        language="en"
    )
    cv.initialize_empty_sections()
    
    test_db.add(cv)
    await test_db.commit()
    await test_db.refresh(cv)
    
    return cv


@pytest.fixture
def sample_user_data() -> dict:
    """Sample user registration data."""
    return {
        "name": "John Doe",
        "email": "<EMAIL>",
        "password": "securepassword123",
        "confirm_password": "securepassword123",
        "language": "en"
    }


@pytest.fixture
def sample_cv_data() -> dict:
    """Sample CV creation data."""
    return {
        "title": "Software Developer CV",
        "template": "modern",
        "language": "en"
    }


@pytest.fixture
def sample_personal_info() -> dict:
    """Sample personal information data."""
    return {
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "address": "123 Main St",
        "city": "New York",
        "postalCode": "10001",
        "country": "USA",
        "dateOfBirth": "1990-01-01",
        "nationality": "American"
    }


@pytest.fixture
def sample_education_data() -> dict:
    """Sample education data."""
    return {
        "education": [
            {
                "id": "1",
                "institution": "University of Technology",
                "degree": "Bachelor of Science",
                "fieldOfStudy": "Computer Science",
                "startDate": "2018-09-01",
                "endDate": "2022-06-01",
                "isCurrentlyStudying": False,
                "grade": "3.8 GPA",
                "description": "Focused on software engineering and algorithms",
                "certificates": []
            }
        ]
    }


@pytest.fixture
def sample_work_experience_data() -> dict:
    """Sample work experience data."""
    return {
        "workExperience": [
            {
                "id": "1",
                "company": "Tech Solutions Inc",
                "position": "Software Developer",
                "startDate": "2022-07-01",
                "endDate": None,
                "isCurrentlyWorking": True,
                "description": "Developing web applications using Python and React",
                "location": "New York, NY"
            }
        ]
    }


@pytest.fixture
def sample_skills_data() -> dict:
    """Sample skills data."""
    return {
        "skills": [
            {
                "id": "1",
                "name": "Python",
                "category": "technical",
                "level": "advanced"
            },
            {
                "id": "2",
                "name": "JavaScript",
                "category": "technical",
                "level": "intermediate"
            },
            {
                "id": "3",
                "name": "English",
                "category": "language",
                "level": "native"
            }
        ]
    }
