"""
Main FastAPI application entry point for CV Maker Backend.

This module initializes the FastAPI application with all necessary
configurations, middleware, and route handlers.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger

from app.core.config import settings
from app.core.database import init_db
from app.core.exceptions import setup_exception_handlers
from app.core.middleware import setup_middleware
from app.core.logging import setup_logging
from app.endpoints import auth, user, cv, files
from app.endpoints import metrics

# Setup logging configuration
setup_logging()

def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.
    
    Returns:
        FastAPI: Configured FastAPI application instance
    """
    app = FastAPI(
        title="CV Maker API",
        description="A comprehensive API for CV creation, management, and export",
        version="1.0.0",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
    )

    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include routers
    app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication"])
    app.include_router(user.router, prefix="/api/v1/user", tags=["User Management"])
    app.include_router(cv.router, prefix="/api/v1/cv", tags=["CV Management"])
    app.include_router(files.router, prefix="/api/v1", tags=["File Management"])
    app.include_router(metrics.router, prefix="/api/v1/metrics", tags=["Metrics & Analytics"])

    # Setup exception handlers
    setup_exception_handlers(app)

    # Setup middleware
    setup_middleware(app)

    @app.on_event("startup")
    async def startup_event():
        """Initialize database on startup."""
        logger.info("Starting CV Maker API...")
        await init_db()
        logger.info("Database initialized successfully")

    @app.get("/")
    async def root():
        """Root endpoint for health check."""
        return {"message": "CV Maker API is running", "version": "1.0.0"}

    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {"status": "healthy", "service": "cv-maker-api"}

    return app

# Create the FastAPI app instance
app = create_app()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
