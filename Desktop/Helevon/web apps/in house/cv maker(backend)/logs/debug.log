2025-07-08 13:45:32 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:48:45 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:48:46 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:48:46 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:48:46 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:48:46 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:48:47 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:48:47 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:48:47 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:49:30 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.37ms - IP: 127.0.0.1
2025-07-08 13:49:49 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 1.36ms - IP: 127.0.0.1
2025-07-08 13:50:07 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 13:50:07 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 523.83ms - IP: 127.0.0.1
2025-07-08 13:50:38 | WARNING  | app.endpoints.auth:register_user:55 | Registration attempt with existing email: <EMAIL>
2025-07-08 13:50:38 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:50:38 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Email already registered - Status: 400
2025-07-08 13:50:38 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 400 - 19.67ms - IP: 127.0.0.1
2025-07-08 13:51:28 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:51:28 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:51:29 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:51:29 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:51:29 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:51:29 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:51:29 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:51:29 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:51:59 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:00 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:00 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:52:00 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:52:00 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:52:00 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:52:00 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:52:00 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:52:00 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.14ms - IP: 127.0.0.1
2025-07-08 13:52:00 | WARNING  | app.endpoints.auth:register_user:55 | Registration attempt with existing email: <EMAIL>
2025-07-08 13:52:00 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:52:00 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Email already registered - Status: 400
2025-07-08 13:52:00 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 400 - 45.59ms - IP: 127.0.0.1
2025-07-08 13:52:19 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:19 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:20 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:52:20 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:52:20 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:52:20 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:52:20 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:52:20 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:52:33 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:33 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:33 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:52:33 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:52:33 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:52:33 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:52:33 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:52:33 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:52:49 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:50 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:50 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:52:50 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:52:50 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:52:50 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:52:50 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:52:50 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:53:08 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:53:08 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:53:08 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:53:08 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:53:08 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:53:08 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:53:08 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:53:08 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:53:23 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:53:23 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:53:23 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:53:23 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:53:23 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:53:23 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:53:23 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:53:23 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:53:34 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.26ms - IP: 127.0.0.1
2025-07-08 13:53:35 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 13:53:35 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 425.58ms - IP: 127.0.0.1
2025-07-08 13:53:35 | DEBUG    | app.core.auth:create_access_token:81 | Access token created for user: ********-b91f-4253-bf1a-4b05eafc91fc
2025-07-08 13:53:35 | DEBUG    | app.core.auth:create_refresh_token:103 | Refresh token created for user: ********-b91f-4253-bf1a-4b05eafc91fc
2025-07-08 13:53:35 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 13:53:35 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 423.35ms - IP: 127.0.0.1
2025-07-08 13:53:35 | DEBUG    | app.endpoints.user:get_user_account:39 | Retrieving account info for user: <EMAIL>
2025-07-08 13:53:35 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 14.01ms - User: ********-b91f-4253-bf1a-4b05eafc91fc - IP: 127.0.0.1
2025-07-08 13:53:35 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:53:35 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Cannot create CV for another user - Status: 403
2025-07-08 13:53:35 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 403 - 15.42ms - User: ********-b91f-4253-bf1a-4b05eafc91fc - IP: 127.0.0.1
2025-07-08 13:54:37 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.24ms - IP: 127.0.0.1
2025-07-08 13:54:37 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 13:54:37 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 365.88ms - IP: 127.0.0.1
2025-07-08 13:54:38 | DEBUG    | app.core.auth:create_access_token:81 | Access token created for user: f1a1a5a6-226f-49ca-8777-a999dedd4b64
2025-07-08 13:54:38 | DEBUG    | app.core.auth:create_refresh_token:103 | Refresh token created for user: f1a1a5a6-226f-49ca-8777-a999dedd4b64
2025-07-08 13:54:38 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 13:54:38 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 620.79ms - IP: 127.0.0.1
2025-07-08 13:54:38 | DEBUG    | app.endpoints.user:get_user_account:39 | Retrieving account info for user: <EMAIL>
2025-07-08 13:54:38 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 12.37ms - User: f1a1a5a6-226f-49ca-8777-a999dedd4b64 - IP: 127.0.0.1
2025-07-08 13:54:38 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:54:38 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Cannot create CV for another user - Status: 403
2025-07-08 13:54:38 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 403 - 20.54ms - User: f1a1a5a6-226f-49ca-8777-a999dedd4b64 - IP: 127.0.0.1
2025-07-08 13:56:03 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.29ms - IP: 127.0.0.1
2025-07-08 13:56:03 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 13:56:03 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 505.20ms - IP: 127.0.0.1
2025-07-08 13:56:03 | DEBUG    | app.core.auth:create_access_token:81 | Access token created for user: 3c492222-3962-4357-8beb-58900582f53f
2025-07-08 13:56:03 | DEBUG    | app.core.auth:create_refresh_token:103 | Refresh token created for user: 3c492222-3962-4357-8beb-58900582f53f
2025-07-08 13:56:03 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 13:56:03 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 439.02ms - IP: 127.0.0.1
2025-07-08 13:56:03 | DEBUG    | app.endpoints.user:get_user_account:39 | Retrieving account info for user: <EMAIL>
2025-07-08 13:56:03 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 15.61ms - User: 3c492222-3962-4357-8beb-58900582f53f - IP: 127.0.0.1
2025-07-08 13:56:04 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:56:04 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Cannot create CV for another user - Status: 403
2025-07-08 13:56:04 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 403 - 15.39ms - User: 3c492222-3962-4357-8beb-58900582f53f - IP: 127.0.0.1
2025-07-08 13:58:08 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 3.19ms - IP: 127.0.0.1
2025-07-08 13:58:09 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 99.47ms - IP: 127.0.0.1
