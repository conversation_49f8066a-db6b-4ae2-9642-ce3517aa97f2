2025-07-08 13:45:32 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:48:45 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:48:46 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:48:46 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:48:46 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:48:46 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:48:47 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:48:47 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:48:47 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:49:30 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.37ms - IP: 127.0.0.1
2025-07-08 13:49:49 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 1.36ms - IP: 127.0.0.1
2025-07-08 13:50:07 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 13:50:07 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 523.83ms - IP: 127.0.0.1
2025-07-08 13:50:38 | WARNING  | app.endpoints.auth:register_user:55 | Registration attempt with existing email: <EMAIL>
2025-07-08 13:50:38 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:50:38 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Email already registered - Status: 400
2025-07-08 13:50:38 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 400 - 19.67ms - IP: 127.0.0.1
2025-07-08 13:51:28 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:51:28 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:51:29 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:51:29 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:51:29 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:51:29 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:51:29 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:51:29 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:51:59 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:00 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:00 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:52:00 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:52:00 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:52:00 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:52:00 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:52:00 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:52:00 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 4.14ms - IP: 127.0.0.1
2025-07-08 13:52:00 | WARNING  | app.endpoints.auth:register_user:55 | Registration attempt with existing email: <EMAIL>
2025-07-08 13:52:00 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:52:00 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Email already registered - Status: 400
2025-07-08 13:52:00 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 400 - 45.59ms - IP: 127.0.0.1
2025-07-08 13:52:19 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:19 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:20 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:52:20 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:52:20 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:52:20 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:52:20 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:52:20 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:52:33 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:33 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:33 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:52:33 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:52:33 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:52:33 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:52:33 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:52:33 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:52:49 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:50 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:52:50 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:52:50 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:52:50 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:52:50 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:52:50 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:52:50 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:53:08 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:53:08 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:53:08 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:53:08 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:53:08 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:53:08 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:53:08 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:53:08 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:53:23 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:53:23 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:53:23 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:53:23 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:53:23 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:53:23 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:53:23 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:53:23 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:53:34 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 3.26ms - IP: 127.0.0.1
2025-07-08 13:53:35 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 13:53:35 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 425.58ms - IP: 127.0.0.1
2025-07-08 13:53:35 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 13:53:35 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 423.35ms - IP: 127.0.0.1
2025-07-08 13:53:35 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 14.01ms - User: ********-b91f-4253-bf1a-4b05eafc91fc - IP: 127.0.0.1
2025-07-08 13:53:35 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:53:35 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Cannot create CV for another user - Status: 403
2025-07-08 13:53:35 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 403 - 15.42ms - User: ********-b91f-4253-bf1a-4b05eafc91fc - IP: 127.0.0.1
2025-07-08 13:54:37 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.24ms - IP: 127.0.0.1
2025-07-08 13:54:37 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 13:54:37 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 365.88ms - IP: 127.0.0.1
2025-07-08 13:54:38 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 13:54:38 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 620.79ms - IP: 127.0.0.1
2025-07-08 13:54:38 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 12.37ms - User: f1a1a5a6-226f-49ca-8777-a999dedd4b64 - IP: 127.0.0.1
2025-07-08 13:54:38 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:54:38 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Cannot create CV for another user - Status: 403
2025-07-08 13:54:38 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 403 - 20.54ms - User: f1a1a5a6-226f-49ca-8777-a999dedd4b64 - IP: 127.0.0.1
2025-07-08 13:56:03 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 1.29ms - IP: 127.0.0.1
2025-07-08 13:56:03 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 13:56:03 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 505.20ms - IP: 127.0.0.1
2025-07-08 13:56:03 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 13:56:03 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 439.02ms - IP: 127.0.0.1
2025-07-08 13:56:03 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 15.61ms - User: 3c492222-3962-4357-8beb-58900582f53f - IP: 127.0.0.1
2025-07-08 13:56:04 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 13:56:04 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Cannot create CV for another user - Status: 403
2025-07-08 13:56:04 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 403 - 15.39ms - User: 3c492222-3962-4357-8beb-58900582f53f - IP: 127.0.0.1
2025-07-08 13:58:08 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 3.19ms - IP: 127.0.0.1
2025-07-08 13:58:09 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 99.47ms - IP: 127.0.0.1
2025-07-08 13:59:39 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:59:40 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:59:40 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:59:40 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:59:40 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:59:40 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:59:40 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:59:40 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 13:59:56 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:59:56 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 13:59:56 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 13:59:56 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 13:59:56 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 13:59:56 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 13:59:56 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 13:59:56 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 14:00:09 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.06ms - IP: 127.0.0.1
2025-07-08 14:00:09 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 14:00:09 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 431.50ms - IP: 127.0.0.1
2025-07-08 14:00:10 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 14:00:10 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 337.17ms - IP: 127.0.0.1
2025-07-08 14:00:10 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 8.64ms - User: 199be5c7-b1de-46ec-8d98-b673d7ef7392 - IP: 127.0.0.1
2025-07-08 14:00:10 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 19.10ms - User: 199be5c7-b1de-46ec-8d98-b673d7ef7392 - IP: 127.0.0.1
2025-07-08 14:00:10 | INFO     | app.endpoints.cv:create_cv:89 | CV created: 063d7ed2-5c0d-47c0-b27b-579d42c32d41 <NAME_EMAIL>
2025-07-08 14:00:10 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 201 - 46.09ms - User: 199be5c7-b1de-46ec-8d98-b673d7ef7392 - IP: 127.0.0.1
2025-07-08 14:00:34 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 14:00:34 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 14:00:34 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 14:00:34 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 14:00:34 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 14:00:34 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 14:00:34 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 14:00:34 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 14:00:51 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 14:00:51 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 14:00:51 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 14:00:51 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 14:00:51 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 14:00:51 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 14:00:51 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 14:00:51 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 14:01:02 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 2.82ms - IP: 127.0.0.1
2025-07-08 14:01:02 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 14:01:02 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 449.03ms - IP: 127.0.0.1
2025-07-08 14:01:03 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 14:01:03 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 418.53ms - IP: 127.0.0.1
2025-07-08 14:01:03 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 16.89ms - User: 3ac93f06-18c7-41a4-953a-00f904a77331 - IP: 127.0.0.1
2025-07-08 14:01:03 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 22.96ms - User: 3ac93f06-18c7-41a4-953a-00f904a77331 - IP: 127.0.0.1
2025-07-08 14:01:03 | INFO     | app.endpoints.cv:create_cv:89 | CV created: e2e61a61-a691-4952-a7a1-36928a902c7c <NAME_EMAIL>
2025-07-08 14:01:03 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 201 - 48.73ms - User: 3ac93f06-18c7-41a4-953a-00f904a77331 - IP: 127.0.0.1
2025-07-08 14:01:04 | ERROR    | app.endpoints.cv:export_cv_pdf:817 | PDF export error: name 'settings' is not defined
2025-07-08 14:01:04 | ERROR    | app.core.database:get_db:108 | Database session error: 
2025-07-08 14:01:04 | WARNING  | app.core.exceptions:http_exception_handler:195 | HTTP exception: Failed to export PDF - Status: 500
2025-07-08 14:01:04 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/e2e61a61-a691-4952-a7a1-36928a902c7c/export - 500 - 513.35ms - User: 3ac93f06-18c7-41a4-953a-00f904a77331 - IP: 127.0.0.1
2025-07-08 14:02:49 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 14:02:49 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 14:02:49 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 14:02:49 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 14:02:49 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 14:02:49 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 14:02:49 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 14:02:49 | INFO     | main:startup_event:65 | Database initialized successfully
2025-07-08 14:03:14 | INFO     | app.core.logging:log_api_access:179 | GET /health - 200 - 5.62ms - IP: 127.0.0.1
2025-07-08 14:03:15 | INFO     | app.endpoints.auth:register_user:84 | New user registered: <EMAIL>
2025-07-08 14:03:15 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/register - 201 - 432.96ms - IP: 127.0.0.1
2025-07-08 14:03:15 | INFO     | app.endpoints.auth:login_user:173 | User logged in: <EMAIL>
2025-07-08 14:03:15 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/auth/signin - 200 - 479.74ms - IP: 127.0.0.1
2025-07-08 14:03:15 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 18.85ms - User: 4548d5d2-a111-420e-9ac8-bf9eac6bb55b - IP: 127.0.0.1
2025-07-08 14:03:15 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/user/account - 200 - 9.07ms - User: 4548d5d2-a111-420e-9ac8-bf9eac6bb55b - IP: 127.0.0.1
2025-07-08 14:03:15 | INFO     | app.endpoints.cv:create_cv:90 | CV created: 0f22329e-7522-48b9-b39a-05186ce32a2c <NAME_EMAIL>
2025-07-08 14:03:15 | INFO     | app.core.logging:log_api_access:179 | POST /api/v1/cv - 201 - 52.28ms - User: 4548d5d2-a111-420e-9ac8-bf9eac6bb55b - IP: 127.0.0.1
2025-07-08 14:03:15 | INFO     | app.services.pdf_service:generate_cv_pdf:136 | PDF generated for CV 0f22329e-7522-48b9-b39a-05186ce32a2c, template: standard
2025-07-08 14:03:15 | INFO     | app.endpoints.cv:export_cv_pdf:803 | PDF exported for CV 0f22329e-7522-48b9-b39a-05186ce32a2c <NAME_EMAIL>
2025-07-08 14:03:15 | INFO     | app.core.logging:log_api_access:179 | GET /api/v1/cv/0f22329e-7522-48b9-b39a-05186ce32a2c/export - 200 - 208.60ms - User: 4548d5d2-a111-420e-9ac8-bf9eac6bb55b - IP: 127.0.0.1
2025-07-08 14:05:21 | INFO     | app.core.logging:log_api_access:179 | GET /docs - 200 - 385.23ms - IP: 127.0.0.1
2025-07-08 14:05:24 | INFO     | app.core.logging:log_api_access:179 | GET /openapi.json - 200 - 1067.46ms - IP: 127.0.0.1
2025-07-08 14:05:24 | WARNING  | app.core.logging:log_performance_issue:228 | Slow Operation: GET /openapi.json - 1067.46ms (threshold: 1000.0ms) - Details: {'user_id': None, 'ip_address': '127.0.0.1', 'user_agent': 'Mozilla/5.0 (X11; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0'}
2025-07-08 15:22:25 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 15:22:25 | INFO     | app.core.logging:setup_logging:288 | Logging configuration initialized successfully
2025-07-08 15:22:25 | INFO     | app.core.exceptions:setup_exception_handlers:268 | Exception handlers configured
2025-07-08 15:22:25 | INFO     | app.core.middleware:setup_middleware:304 | Security and utility middleware configured
2025-07-08 15:22:25 | INFO     | main:startup_event:63 | Starting CV Maker API...
2025-07-08 15:22:25 | INFO     | app.core.database:create_tables:77 | Database tables created successfully
2025-07-08 15:22:25 | INFO     | app.core.database:init_db:118 | Database initialized successfully
2025-07-08 15:22:25 | INFO     | main:startup_event:65 | Database initialized successfully
